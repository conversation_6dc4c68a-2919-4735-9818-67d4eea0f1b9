<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>BareExpo</groupId>
  <artifactId>expo.modules.image</artifactId>
  <version>2.4.0</version>
  <packaging>aar</packaging>
  <name>expo.modules.image</name>
  <url>https://github.com/expo/expo</url>
  <licenses>
    <license>
      <name>MIT License</name>
      <url>https://github.com/expo/expo/blob/main/LICENSE</url>
    </license>
  </licenses>
  <scm>
    <connection>https://github.com/expo/expo.git</connection>
    <developerConnection>https://github.com/expo/expo.git</developerConnection>
    <url>https://github.com/expo/expo</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.github.bumptech.glide</groupId>
      <artifactId>glide</artifactId>
      <version>4.16.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.caverock</groupId>
      <artifactId>androidsvg-aar</artifactId>
      <version>1.4</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.github.bumptech.glide</groupId>
      <artifactId>okhttp3-integration</artifactId>
      <version>4.11.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>4.9.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib-jdk7</artifactId>
      <version>2.0.21</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.facebook.react</groupId>
      <artifactId>react-android</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.github.penfeizhou.android.animation</groupId>
      <artifactId>glide-plugin</artifactId>
      <version>3.0.5</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.github.bumptech.glide</groupId>
      <artifactId>avif-integration</artifactId>
      <version>4.16.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlinx</groupId>
      <artifactId>kotlinx-coroutines-core</artifactId>
      <version>1.5.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>jp.wasabeef</groupId>
      <artifactId>glide-transformations</artifactId>
      <version>4.3.0</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>
