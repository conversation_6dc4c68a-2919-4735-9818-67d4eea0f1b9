import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { comickAPI, getCoverImageUrl, formatStatus, formatContentRating } from '../services/api';

const { width } = Dimensions.get('window');

export default function ManhwaDetailScreen({ route, navigation }) {
  const { slug, hid, title } = route.params;
  const [comic, setComic] = useState(null);
  const [chapters, setChapters] = useState([]);
  const [allChapters, setAllChapters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [chaptersLoading, setChaptersLoading] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);

  useEffect(() => {
    loadComicDetails();
  }, []);

  const loadComicDetails = async () => {
    try {
      setLoading(true);
      const comicData = await comickAPI.getComicDetails(slug);
      setComic(comicData.comic);

      // Use the hid from comic data if not provided in params
      const comicHid = hid || comicData.comic?.hid;

      if (comicHid) {
        await loadAllChapters(comicHid);
      }
    } catch (error) {
      console.error('Error loading comic details:', error);
      Alert.alert('Error', 'Failed to load comic details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadAllChapters = async (comicHid) => {
    try {
      let allChaptersData = [];
      let page = 0;
      let hasMore = true;

      // Load all chapters by paginating
      while (hasMore) {
        const chaptersData = await comickAPI.getComicChapters(comicHid, { page, limit: 100 });
        const pageChapters = chaptersData.chapters || [];

        if (pageChapters.length === 0) {
          hasMore = false;
        } else {
          allChaptersData = [...allChaptersData, ...pageChapters];
          page++;
        }
      }

      // Filter for English chapters only and sort by chapter number
      const englishChapters = allChaptersData
        .filter(chapter => chapter.lang === 'en')
        .sort((a, b) => {
          const aChap = parseFloat(a.chap) || 0;
          const bChap = parseFloat(b.chap) || 0;
          return bChap - aChap; // Descending order (latest first)
        });

      setAllChapters(allChaptersData);
      setChapters(englishChapters);
    } catch (error) {
      console.error('Error loading all chapters:', error);
    }
  };



  const renderChapterItem = ({ item }) => (
    <TouchableOpacity
      style={styles.chapterItem}
      onPress={() => {
        navigation.navigate('ChapterReader', {
          chapterHid: item.hid,
          chapterNumber: item.chap,
          comicTitle: comic.title,
          comicSlug: slug,
          comicHid: hid || comic?.hid,
          allChapters: allChapters,
          currentChapter: item
        });
      }}
    >
      <View style={styles.chapterInfo}>
        <Text style={styles.chapterTitle}>
          Chapter {item.chap}
          {item.title && ` - ${item.title}`}
        </Text>
        <Text style={styles.chapterGroup}>
          {item.group_name?.[0] || 'Unknown'}
        </Text>
        <Text style={styles.chapterDate}>
          {new Date(item.created_at).toLocaleDateString()}
        </Text>
      </View>
      <View style={styles.chapterStats}>
        <View style={styles.statItem}>
          <Ionicons name="thumbs-up-outline" size={12} color="#4CAF50" />
          <Text style={styles.statText}>{item.up_count || 0}</Text>
        </View>
        <Ionicons name="chevron-forward" size={16} color="#666" />
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ff6b6b" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (!comic) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load comic details</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header Section */}
      <View style={styles.header}>
        <Image
          source={{ uri: getCoverImageUrl(comic.md_covers?.[0]) }}
          style={styles.coverImage}
          contentFit="cover"
          placeholder="https://via.placeholder.com/150x200/333/fff?text=No+Image"
        />
        <View style={styles.headerInfo}>
          <Text style={styles.title}>{comic.title}</Text>
          <Text style={styles.status}>
            {formatStatus(comic.status)} • {formatContentRating(comic.content_rating)}
          </Text>
          <Text style={styles.chapters}>
            {comic.last_chapter} Chapters
          </Text>
          {comic.bayesian_rating && (
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={16} color="#ffd700" />
              <Text style={styles.rating}>
                {parseFloat(comic.bayesian_rating).toFixed(1)}
              </Text>
              <Text style={styles.ratingCount}>
                ({comic.rating_count} ratings)
              </Text>
            </View>
          )}
          <View style={styles.followContainer}>
            <Ionicons name="people" size={16} color="#ff6b6b" />
            <Text style={styles.followCount}>
              {comic.user_follow_count?.toLocaleString()} followers
            </Text>
          </View>
        </View>
      </View>

      {/* Description Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Description</Text>
        <Text style={styles.description} numberOfLines={showFullDescription ? undefined : 4}>
          {comic.desc || 'No description available.'}
        </Text>
        {comic.desc && comic.desc.length > 200 && (
          <TouchableOpacity onPress={() => setShowFullDescription(!showFullDescription)}>
            <Text style={styles.showMoreText}>
              {showFullDescription ? 'Show Less' : 'Show More'}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Genres Section */}
      {comic.md_comic_md_genres && comic.md_comic_md_genres.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Genres</Text>
          <View style={styles.genresContainer}>
            {comic.md_comic_md_genres.slice(0, 8).map((genre, index) => (
              <View key={index} style={styles.genreTag}>
                <Text style={styles.genreText}>{genre.md_genres.name}</Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Chapters Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Chapters ({chapters.length}) - English Only
        </Text>
        <FlatList
          data={chapters}
          renderItem={renderChapterItem}
          keyExtractor={(item) => `${item.id}-${item.hid}`}
          scrollEnabled={false}
          ListFooterComponent={
            chaptersLoading ? (
              <View style={styles.loadingMore}>
                <ActivityIndicator size="small" color="#ff6b6b" />
                <Text style={styles.loadingMoreText}>Loading chapters...</Text>
              </View>
            ) : null
          }
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0a0a0a',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0a0a0a',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0a0a0a',
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    padding: 20,
    backgroundColor: '#1a1a1a',
  },
  coverImage: {
    width: 120,
    height: 160,
    borderRadius: 8,
  },
  headerInfo: {
    flex: 1,
    marginLeft: 15,
    justifyContent: 'space-between',
  },
  title: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  status: {
    color: '#ff6b6b',
    fontSize: 14,
    marginBottom: 4,
  },
  chapters: {
    color: '#888',
    fontSize: 12,
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  rating: {
    color: '#ffd700',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  ratingCount: {
    color: '#666',
    fontSize: 12,
    marginLeft: 4,
  },
  followContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  followCount: {
    color: '#ff6b6b',
    fontSize: 12,
    marginLeft: 4,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  description: {
    color: '#ccc',
    fontSize: 14,
    lineHeight: 20,
  },
  showMoreText: {
    color: '#ff6b6b',
    fontSize: 14,
    marginTop: 8,
  },
  genresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  genreTag: {
    backgroundColor: '#333',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    marginRight: 8,
    marginBottom: 8,
  },
  genreText: {
    color: '#fff',
    fontSize: 12,
  },
  chapterItem: {
    flexDirection: 'row',
    backgroundColor: '#1a1a1a',
    padding: 15,
    marginBottom: 8,
    borderRadius: 8,
    alignItems: 'center',
  },
  chapterInfo: {
    flex: 1,
  },
  chapterTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  chapterGroup: {
    color: '#888',
    fontSize: 12,
    marginBottom: 2,
  },
  chapterDate: {
    color: '#666',
    fontSize: 10,
  },
  chapterStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
  },
  statText: {
    color: '#4CAF50',
    fontSize: 12,
    marginLeft: 2,
  },
  loadingMore: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingMoreText: {
    color: '#666',
    marginLeft: 10,
  },
});
