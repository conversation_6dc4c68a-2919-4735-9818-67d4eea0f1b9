import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { comickAPI, getCoverImageUrl, formatStatus, formatContentRating } from '../services/api';

const { width } = Dimensions.get('window');

export default function ManhwaDetailScreen({ route, navigation }) {
  const { slug, hid, title } = route.params;
  const [comic, setComic] = useState(null);
  const [chapters, setChapters] = useState([]);
  const [allChapters, setAllChapters] = useState([]);
  const [groupedChapters, setGroupedChapters] = useState([]);
  const [expandedChapters, setExpandedChapters] = useState(new Set());
  const [loading, setLoading] = useState(true);
  const [chaptersLoading, setChaptersLoading] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);

  useEffect(() => {
    loadComicDetails();
  }, []);

  const loadComicDetails = async () => {
    try {
      setLoading(true);
      const comicData = await comickAPI.getComicDetails(slug);
      setComic(comicData.comic);

      // Use the hid from comic data if not provided in params
      const comicHid = hid || comicData.comic?.hid;

      if (comicHid) {
        await loadAllChapters(comicHid);
      }
    } catch (error) {
      console.error('Error loading comic details:', error);
      Alert.alert('Error', 'Failed to load comic details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadAllChapters = async (comicHid) => {
    try {
      let allChaptersData = [];
      let page = 0;
      let hasMore = true;

      // Load all chapters by paginating
      while (hasMore) {
        const chaptersData = await comickAPI.getComicChapters(comicHid, { page, limit: 100 });
        const pageChapters = chaptersData.chapters || [];

        if (pageChapters.length === 0) {
          hasMore = false;
        } else {
          allChaptersData = [...allChaptersData, ...pageChapters];
          page++;
        }
      }

      // Filter for English chapters only
      const englishChapters = allChaptersData.filter(chapter => chapter.lang === 'en');

      // Group chapters by chapter number
      const chapterGroups = {};
      englishChapters.forEach(chapter => {
        const chapNum = chapter.chap;
        if (!chapterGroups[chapNum]) {
          chapterGroups[chapNum] = [];
        }
        chapterGroups[chapNum].push(chapter);
      });

      // Convert to array and sort by chapter number (descending)
      const groupedChaptersArray = Object.entries(chapterGroups)
        .map(([chapNum, chapters]) => ({
          chapterNumber: chapNum,
          chapters: chapters.sort((a, b) => new Date(b.created_at) - new Date(a.created_at)), // Latest scan first
          defaultChapter: chapters[0] // First scan group as default
        }))
        .sort((a, b) => {
          const aNum = parseFloat(a.chapterNumber) || 0;
          const bNum = parseFloat(b.chapterNumber) || 0;
          return bNum - aNum; // Descending order (latest first)
        });

      setAllChapters(allChaptersData);
      setGroupedChapters(groupedChaptersArray);
      setChapters(englishChapters);
    } catch (error) {
      console.error('Error loading all chapters:', error);
    }
  };



  const toggleChapterExpansion = (chapterNumber) => {
    const newExpanded = new Set(expandedChapters);
    if (newExpanded.has(chapterNumber)) {
      newExpanded.delete(chapterNumber);
    } else {
      newExpanded.add(chapterNumber);
    }
    setExpandedChapters(newExpanded);
  };

  const navigateToChapter = (chapter) => {
    navigation.navigate('ChapterReader', {
      chapterHid: chapter.hid,
      chapterNumber: chapter.chap,
      comicTitle: comic.title,
      comicSlug: slug,
      comicHid: hid || comic?.hid,
      allChapters: allChapters,
      currentChapter: chapter
    });
  };

  const renderChapterGroup = ({ item }) => {
    const isExpanded = expandedChapters.has(item.chapterNumber);
    const hasMultipleScans = item.chapters.length > 1;

    return (
      <View style={styles.chapterGroup}>
        {/* Main Chapter Row */}
        <View style={styles.chapterMainRow}>
          <TouchableOpacity
            style={styles.chapterMainButton}
            onPress={() => navigateToChapter(item.defaultChapter)}
          >
            <View style={styles.chapterInfo}>
              <Text style={styles.chapterTitle}>
                Chapter {item.chapterNumber}
                {item.defaultChapter.title && ` - ${item.defaultChapter.title}`}
              </Text>
              <Text style={styles.chapterGroup}>
                {item.defaultChapter.group_name?.[0] || 'Unknown'}
              </Text>
              <Text style={styles.chapterDate}>
                {new Date(item.defaultChapter.created_at).toLocaleDateString()}
              </Text>
            </View>
            <View style={styles.chapterStats}>
              <View style={styles.statItem}>
                <Ionicons name="thumbs-up-outline" size={12} color="#4CAF50" />
                <Text style={styles.statText}>{item.defaultChapter.up_count || 0}</Text>
              </View>
            </View>
          </TouchableOpacity>

          {/* Expand Button */}
          {hasMultipleScans && (
            <TouchableOpacity
              style={styles.expandButton}
              onPress={() => toggleChapterExpansion(item.chapterNumber)}
            >
              <Text style={styles.scanGroupCount}>{item.chapters.length}</Text>
              <Ionicons
                name={isExpanded ? "chevron-up" : "chevron-down"}
                size={16}
                color="#666"
              />
            </TouchableOpacity>
          )}
        </View>

        {/* Expanded Scan Groups */}
        {isExpanded && hasMultipleScans && (
          <View style={styles.expandedScans}>
            {item.chapters.map((chapter, index) => (
              <TouchableOpacity
                key={`${chapter.hid}-${index}`}
                style={styles.scanGroupItem}
                onPress={() => navigateToChapter(chapter)}
              >
                <Text style={styles.scanGroupName}>
                  {chapter.group_name?.[0] || 'Unknown'}
                </Text>
                <Text style={styles.scanGroupDate}>
                  {new Date(chapter.created_at).toLocaleDateString()}
                </Text>
                <View style={styles.scanGroupStats}>
                  <Ionicons name="thumbs-up-outline" size={10} color="#4CAF50" />
                  <Text style={styles.scanGroupStatText}>{chapter.up_count || 0}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    );
  };

  const renderChapter = ({ item }) => (
    <View style={styles.chapterItem}>
      <View style={styles.chapterInfo}>
        <Text style={styles.chapterTitle}>
          Chapter {item.chap}
          {item.title && ` - ${item.title}`}
        </Text>
        <Text style={styles.chapterGroup}>
          {item.group_name?.[0] || 'Unknown'}
        </Text>
        <Text style={styles.chapterDate}>
          {new Date(item.created_at).toLocaleDateString()}
        </Text>
      </View>
      <View style={styles.chapterStats}>
        <View style={styles.statItem}>
          <Ionicons name="thumbs-up-outline" size={12} color="#4CAF50" />
          <Text style={styles.statText}>{item.up_count || 0}</Text>
        </View>
        <Ionicons name="chevron-forward" size={16} color="#666" />
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ff6b6b" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (!comic) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load comic details</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header Section */}
      <View style={styles.header}>
        <Image
          source={{ uri: getCoverImageUrl(comic.md_covers?.[0]) }}
          style={styles.coverImage}
          contentFit="cover"
          placeholder="https://via.placeholder.com/150x200/333/fff?text=No+Image"
        />
        <View style={styles.headerInfo}>
          <Text style={styles.title}>{comic.title}</Text>
          <Text style={styles.status}>
            {formatStatus(comic.status)} • {formatContentRating(comic.content_rating)}
          </Text>
          <Text style={styles.chapters}>
            {comic.last_chapter} Chapters
          </Text>
          {comic.bayesian_rating && (
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={16} color="#ffd700" />
              <Text style={styles.rating}>
                {parseFloat(comic.bayesian_rating).toFixed(1)}
              </Text>
              <Text style={styles.ratingCount}>
                ({comic.rating_count} ratings)
              </Text>
            </View>
          )}
          <View style={styles.followContainer}>
            <Ionicons name="people" size={16} color="#ff6b6b" />
            <Text style={styles.followCount}>
              {comic.user_follow_count?.toLocaleString()} followers
            </Text>
          </View>
        </View>
      </View>

      {/* Description Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Description</Text>
        <Text style={styles.description} numberOfLines={showFullDescription ? undefined : 4}>
          {comic.desc || 'No description available.'}
        </Text>
        {comic.desc && comic.desc.length > 200 && (
          <TouchableOpacity onPress={() => setShowFullDescription(!showFullDescription)}>
            <Text style={styles.showMoreText}>
              {showFullDescription ? 'Show Less' : 'Show More'}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Genres Section */}
      {comic.md_comic_md_genres && comic.md_comic_md_genres.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Genres</Text>
          <View style={styles.genresContainer}>
            {comic.md_comic_md_genres.slice(0, 8).map((genre, index) => (
              <View key={index} style={styles.genreTag}>
                <Text style={styles.genreText}>{genre.md_genres.name}</Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Chapters Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Chapters ({groupedChapters.length}) - English Only
        </Text>
        <FlatList
          data={groupedChapters}
          renderItem={renderChapterGroup}
          keyExtractor={(item) => `chapter-${item.chapterNumber}`}
          scrollEnabled={false}
          ListFooterComponent={
            chaptersLoading ? (
              <View style={styles.loadingMore}>
                <ActivityIndicator size="small" color="#ff6b6b" />
                <Text style={styles.loadingMoreText}>Loading chapters...</Text>
              </View>
            ) : null
          }
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0a0a0a',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0a0a0a',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0a0a0a',
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    padding: 20,
    backgroundColor: '#1a1a1a',
  },
  coverImage: {
    width: 120,
    height: 160,
    borderRadius: 8,
  },
  headerInfo: {
    flex: 1,
    marginLeft: 15,
    justifyContent: 'space-between',
  },
  title: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  status: {
    color: '#ff6b6b',
    fontSize: 14,
    marginBottom: 4,
  },
  chapters: {
    color: '#888',
    fontSize: 12,
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  rating: {
    color: '#ffd700',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  ratingCount: {
    color: '#666',
    fontSize: 12,
    marginLeft: 4,
  },
  followContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  followCount: {
    color: '#ff6b6b',
    fontSize: 12,
    marginLeft: 4,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  description: {
    color: '#ccc',
    fontSize: 14,
    lineHeight: 20,
  },
  showMoreText: {
    color: '#ff6b6b',
    fontSize: 14,
    marginTop: 8,
  },
  genresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  genreTag: {
    backgroundColor: '#333',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    marginRight: 8,
    marginBottom: 8,
  },
  genreText: {
    color: '#fff',
    fontSize: 12,
  },
  chapterGroup: {
    marginBottom: 8,
    backgroundColor: '#1a1a1a',
    borderRadius: 8,
    overflow: 'hidden',
  },
  chapterMainRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  chapterMainButton: {
    flex: 1,
    flexDirection: 'row',
    padding: 15,
    alignItems: 'center',
  },
  chapterInfo: {
    flex: 1,
  },
  chapterTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  chapterGroup: {
    color: '#888',
    fontSize: 12,
    marginBottom: 2,
  },
  chapterDate: {
    color: '#666',
    fontSize: 10,
  },
  chapterStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
  },
  statText: {
    color: '#4CAF50',
    fontSize: 12,
    marginLeft: 2,
  },
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 15,
    backgroundColor: '#333',
  },
  scanGroupCount: {
    color: '#ff6b6b',
    fontSize: 12,
    fontWeight: 'bold',
    marginRight: 5,
  },
  expandedScans: {
    backgroundColor: '#0f0f0f',
    paddingHorizontal: 15,
  },
  scanGroupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  scanGroupName: {
    flex: 1,
    color: '#ccc',
    fontSize: 13,
  },
  scanGroupDate: {
    color: '#666',
    fontSize: 10,
    marginRight: 10,
  },
  scanGroupStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scanGroupStatText: {
    color: '#4CAF50',
    fontSize: 10,
    marginLeft: 2,
  },
  loadingMore: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingMoreText: {
    color: '#666',
    marginLeft: 10,
  },
});
