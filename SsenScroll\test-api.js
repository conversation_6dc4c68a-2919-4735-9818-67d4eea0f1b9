// Simple test script to verify API endpoints
const { comickAPI } = require('./services/api');

async function testAPI() {
  console.log('Testing Comick API endpoints...\n');

  try {
    // Test trending comics
    console.log('1. Testing trending comics...');
    const trending = await comickAPI.getTrendingComics();
    console.log(`✅ Trending comics: Found ${trending.rank?.length || 0} comics`);
    if (trending.rank?.[0]) {
      console.log(`   First comic: ${trending.rank[0].title}`);
    }
    console.log('');

    // Test latest chapters
    console.log('2. Testing latest chapters...');
    const latest = await comickAPI.getLatestChapters({ limit: 5 });
    console.log(`✅ Latest chapters: Found ${latest?.length || 0} chapters`);
    if (latest?.[0]) {
      console.log(`   First chapter: ${latest[0].md_comics?.title} - Ch. ${latest[0].chap}`);
    }
    console.log('');

    // Test search
    console.log('3. Testing search...');
    const searchResults = await comickAPI.searchComics('solo leveling', { limit: 3 });
    console.log(`✅ Search results: Found ${searchResults?.length || 0} results`);
    if (searchResults?.[0]) {
      console.log(`   First result: ${searchResults[0].title}`);
    }
    console.log('');

    // Test comic details
    if (searchResults?.[0]?.slug) {
      console.log('4. Testing comic details...');
      const comicDetails = await comickAPI.getComicDetails(searchResults[0].slug);
      console.log(`✅ Comic details: ${comicDetails.comic?.title}`);
      console.log(`   Status: ${comicDetails.comic?.status}`);
      console.log(`   Chapters: ${comicDetails.comic?.last_chapter}`);
      console.log('');

      // Test chapters list
      if (comicDetails.comic?.hid) {
        console.log('5. Testing chapters list...');
        const chapters = await comickAPI.getComicChapters(comicDetails.comic.hid, { limit: 3 });
        console.log(`✅ Chapters list: Found ${chapters.chapters?.length || 0} chapters`);
        if (chapters.chapters?.[0]) {
          console.log(`   Latest chapter: Ch. ${chapters.chapters[0].chap}`);
        }
      }
    }

    console.log('\n🎉 All API tests passed!');
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

testAPI();
