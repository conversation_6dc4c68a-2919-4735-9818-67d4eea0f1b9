// Test script to verify our fixes
const { comickAPI, groupChaptersByScanlator, getLanguageDisplayName } = require('./services/api');

async function testFixes() {
  console.log('Testing fixes...\n');

  try {
    // Test 1: Search for zombie papa and check chapters grouping
    console.log('1. Testing zombie papa chapters grouping...');
    const searchResults = await comickAPI.searchComics('zombie papa', { limit: 1 });
    
    if (searchResults?.[0]) {
      const comic = searchResults[0];
      console.log(`✅ Found comic: ${comic.title}`);
      console.log(`   HID: ${comic.hid}`);
      
      if (comic.hid) {
        const chaptersData = await comickAPI.getComicChapters(comic.hid, { limit: 20 });
        const chapters = chaptersData.chapters || [];
        console.log(`✅ Found ${chapters.length} chapters`);
        
        // Group chapters
        const grouped = groupChaptersByScanlator(chapters);
        console.log(`✅ Grouped into ${Object.keys(grouped).length} scan groups:`);
        
        Object.entries(grouped).forEach(([key, group]) => {
          console.log(`   - ${group.groupName} (${getLanguageDisplayName(group.language)}): ${group.chapters.length} chapters`);
        });
      }
    }
    console.log('');

    // Test 2: Test chapter images
    console.log('2. Testing chapter images...');
    const testChapterHid = 'm6nYOIcN'; // From our earlier test
    const images = await comickAPI.getChapterImages(testChapterHid);
    console.log(`✅ Found ${images?.length || 0} images for chapter`);
    if (images?.[0]) {
      console.log(`   First image: ${images[0].w}x${images[0].h}`);
      console.log(`   B2Key: ${images[0].b2key}`);
    }
    console.log('');

    // Test 3: Test trending comics
    console.log('3. Testing trending comics...');
    const trending = await comickAPI.getTrendingComics();
    console.log(`✅ Found ${trending.rank?.length || 0} trending comics`);
    if (trending.rank?.[0]) {
      const comic = trending.rank[0];
      console.log(`   First comic: ${comic.title}`);
      console.log(`   Status: ${comic.status || 'No status'}`);
      console.log(`   HID: ${comic.hid || 'No HID'}`);
    }

    console.log('\n🎉 All tests passed!');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testFixes();
