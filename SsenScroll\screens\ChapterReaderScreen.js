import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Alert,
  Dimensions,
  StatusBar,
  TouchableOpacity,
  Modal,
  FlatList,
  SafeAreaView,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { comickAPI, getChapterImageUrl, groupChaptersByScanlator, getLanguageDisplayName } from '../services/api';

const { width, height } = Dimensions.get('window');

export default function ChapterReaderScreen({ route, navigation }) {
  const {
    chapterHid,
    chapterNumber,
    comicTitle,
    comicSlug,
    comicHid,
    allChapters,
    currentChapter
  } = route.params;

  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showControls, setShowControls] = useState(false);
  const [showChapterModal, setShowChapterModal] = useState(false);
  const [showScanlatorModal, setShowScanlatorModal] = useState(false);
  const [currentChapterData, setCurrentChapterData] = useState(currentChapter);
  const [availableScanlators, setAvailableScanlators] = useState([]);
  const [englishChapters, setEnglishChapters] = useState([]);
  const scrollViewRef = useRef(null);

  useEffect(() => {
    // Hide navigation header completely
    navigation.setOptions({
      headerShown: false,
    });

    if (currentChapter) {
      setCurrentChapterData(currentChapter);
      loadChapterImages();
      prepareChapterData();
    }
  }, []);

  useEffect(() => {
    if (currentChapterData && currentChapterData.hid) {
      loadChapterImages();
      findAvailableScanlators();
    }
  }, [currentChapterData]);

  const prepareChapterData = () => {
    if (!allChapters || allChapters.length === 0) return;

    // Filter English chapters and sort by chapter number
    const englishOnly = allChapters
      .filter(chapter => chapter.lang === 'en')
      .sort((a, b) => {
        const aChap = parseFloat(a.chap) || 0;
        const bChap = parseFloat(b.chap) || 0;
        return bChap - aChap; // Descending order
      });

    setEnglishChapters(englishOnly);
  };

  const findAvailableScanlators = () => {
    if (!currentChapterData || !allChapters) return;

    // Find all scanlators for the current chapter number
    const sameChapterNumber = allChapters.filter(
      chapter => chapter.chap === currentChapterData.chap && chapter.lang === 'en'
    );
    setAvailableScanlators(sameChapterNumber);
  };

  const loadChapterImages = async () => {
    if (!currentChapterData || !currentChapterData.hid) {
      console.log('No chapter data or hid available');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      console.log('Loading images for chapter:', currentChapterData.hid);
      const chapterImages = await comickAPI.getChapterImages(currentChapterData.hid);
      console.log('Loaded images:', chapterImages?.length || 0);
      setImages(chapterImages || []);

      // Scroll to top when new chapter loads
      setTimeout(() => {
        scrollViewRef.current?.scrollToOffset({ offset: 0, animated: false });
      }, 100);
    } catch (error) {
      console.error('Error loading chapter images:', error);
      Alert.alert('Error', `Failed to load chapter images: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const goToNextChapter = () => {
    if (!currentChapterData || !englishChapters.length) return;

    const currentIndex = englishChapters.findIndex(ch => ch.hid === currentChapterData.hid);
    if (currentIndex > 0) {
      setCurrentChapterData(englishChapters[currentIndex - 1]);
    }
  };

  const goToPreviousChapter = () => {
    if (!currentChapterData || !englishChapters.length) return;

    const currentIndex = englishChapters.findIndex(ch => ch.hid === currentChapterData.hid);
    if (currentIndex < englishChapters.length - 1) {
      setCurrentChapterData(englishChapters[currentIndex + 1]);
    }
  };

  const selectChapter = (chapter) => {
    setCurrentChapterData(chapter);
    setShowChapterModal(false);
  };

  const selectScanlator = (chapter) => {
    setCurrentChapterData(chapter);
    setShowScanlatorModal(false);
  };

  const toggleControls = () => {
    setShowControls(!showControls);
  };

  const goBack = () => {
    navigation.goBack();
  };

  const renderChapterImage = ({ item, index }) => {
    const imageHeight = item.h && item.w ? (item.h / item.w) * width : width * 1.5;

    return (
      <Image
        source={{ uri: getChapterImageUrl(item) }}
        style={[
          styles.chapterImage,
          {
            width: width,
            height: imageHeight,
          }
        ]}
        contentFit="cover"
        placeholder="https://via.placeholder.com/800x1200/333/fff?text=Loading..."
        cachePolicy="memory-disk"
        transition={0}
      />
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <StatusBar hidden />
        <ActivityIndicator size="large" color="#ff6b6b" />
        <Text style={styles.loadingText}>Loading chapter...</Text>
      </View>
    );
  }

  if (images.length === 0) {
    return (
      <View style={styles.errorContainer}>
        <StatusBar hidden />
        <Text style={styles.errorText}>No images found for this chapter</Text>
        <TouchableOpacity style={styles.backButton} onPress={goBack}>
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar hidden />

      {/* Top Controls */}
      {showControls && (
        <SafeAreaView style={styles.topControlsContainer}>
          <View style={styles.topControls}>
            <TouchableOpacity style={styles.controlButton} onPress={goBack}>
              <Ionicons name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.controlButton}
              onPress={() => setShowChapterModal(true)}
            >
              <Text style={styles.controlButtonText}>
                Ch. {currentChapterData?.chap || 'Unknown'}
              </Text>
              <Ionicons name="chevron-down" size={16} color="#fff" />
            </TouchableOpacity>

            {availableScanlators.length > 1 && (
              <TouchableOpacity
                style={styles.controlButton}
                onPress={() => setShowScanlatorModal(true)}
              >
                <Text style={styles.controlButtonText}>
                  {currentChapterData?.group_name?.[0] || 'Unknown'}
                </Text>
                <Ionicons name="chevron-down" size={16} color="#fff" />
              </TouchableOpacity>
            )}
          </View>
        </SafeAreaView>
      )}

      {/* Main Reading Area */}
      <TouchableOpacity
        style={styles.readingArea}
        activeOpacity={1}
        onPress={toggleControls}
      >
        <FlatList
          ref={scrollViewRef}
          data={images}
          renderItem={renderChapterImage}
          keyExtractor={(item, index) => `image-${index}`}
          showsVerticalScrollIndicator={false}
          bounces={false}
          removeClippedSubviews={false}
          maxToRenderPerBatch={5}
          windowSize={10}
          initialNumToRender={3}
          ListFooterComponent={
            <View style={styles.endOfChapter}>
              <Text style={styles.endText}>End of Chapter {currentChapterData?.chap || 'Unknown'}</Text>
              <Text style={styles.endSubtext}>
                Scanned by {currentChapterData?.group_name?.[0] || 'Unknown'}
              </Text>
            </View>
          }
        />
      </TouchableOpacity>

      {/* Bottom Controls */}
      {showControls && (
        <SafeAreaView style={styles.bottomControlsContainer}>
          <View style={styles.bottomControls}>
            <TouchableOpacity
              style={[styles.navButton, {
                opacity: !currentChapterData || !englishChapters.length ||
                         englishChapters.findIndex(ch => ch.hid === currentChapterData.hid) >= englishChapters.length - 1 ? 0.3 : 1
              }]}
              onPress={goToPreviousChapter}
              disabled={!currentChapterData || !englishChapters.length ||
                       englishChapters.findIndex(ch => ch.hid === currentChapterData.hid) >= englishChapters.length - 1}
            >
              <Ionicons name="chevron-back" size={24} color="#fff" />
              <Text style={styles.navButtonText}>Previous</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.navButton, {
                opacity: !currentChapterData || !englishChapters.length ||
                         englishChapters.findIndex(ch => ch.hid === currentChapterData.hid) <= 0 ? 0.3 : 1
              }]}
              onPress={goToNextChapter}
              disabled={!currentChapterData || !englishChapters.length ||
                       englishChapters.findIndex(ch => ch.hid === currentChapterData.hid) <= 0}
            >
              <Text style={styles.navButtonText}>Next</Text>
              <Ionicons name="chevron-forward" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      )}



      {/* Chapter Selection Modal */}
      <Modal
        visible={showChapterModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowChapterModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Chapter</Text>
              <TouchableOpacity onPress={() => setShowChapterModal(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            <FlatList
              data={englishChapters}
              keyExtractor={(item, index) => `chapter-modal-${item.hid}-${index}`}
              maxToRenderPerBatch={10}
              windowSize={10}
              initialNumToRender={10}
              removeClippedSubviews={true}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.modalItem,
                    item.hid === currentChapterData?.hid && styles.modalItemActive
                  ]}
                  onPress={() => selectChapter(item)}
                >
                  <Text style={styles.modalItemText}>
                    Chapter {item.chap}
                    {item.title && ` - ${item.title}`}
                  </Text>
                  <Text style={styles.modalItemSubtext}>
                    {item.group_name?.[0] || 'Unknown'}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </Modal>

      {/* Scanlator Selection Modal */}
      <Modal
        visible={showScanlatorModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowScanlatorModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Scan Group</Text>
              <TouchableOpacity onPress={() => setShowScanlatorModal(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            <FlatList
              data={availableScanlators}
              keyExtractor={(item, index) => `scanlator-modal-${item.hid}-${index}`}
              maxToRenderPerBatch={5}
              windowSize={5}
              initialNumToRender={5}
              removeClippedSubviews={true}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.modalItem,
                    item.hid === currentChapterData?.hid && styles.modalItemActive
                  ]}
                  onPress={() => selectScanlator(item)}
                >
                  <Text style={styles.modalItemText}>
                    {item.group_name?.[0] || 'Unknown'}
                  </Text>
                  <Text style={styles.modalItemSubtext}>
                    {new Date(item.created_at).toLocaleDateString()}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
  backButton: {
    backgroundColor: '#ff6b6b',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
    marginTop: 20,
  },
  backButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  readingArea: {
    flex: 1,
  },
  chapterImage: {
    backgroundColor: '#000',
  },
  endOfChapter: {
    backgroundColor: '#1a1a1a',
    paddingVertical: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  endText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  endSubtext: {
    color: '#888',
    fontSize: 14,
  },
  topControlsContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  bottomControlsContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderTopWidth: 1,
    borderTopColor: '#333',
  },
  topControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  bottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  controlButton: {
    backgroundColor: '#333',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  controlButtonText: {
    color: '#fff',
    fontSize: 14,
    marginRight: 5,
  },
  navButton: {
    backgroundColor: '#ff6b6b',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderRadius: 25,
    flexDirection: 'row',
    alignItems: 'center',
  },
  navButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#1a1a1a',
    borderRadius: 10,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  modalTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  modalItemActive: {
    backgroundColor: '#ff6b6b',
  },
  modalItemText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalItemSubtext: {
    color: '#ccc',
    fontSize: 12,
    marginTop: 4,
  },
});
