import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Alert,
  Dimensions,
  StatusBar,
} from 'react-native';
import { Image } from 'expo-image';
import { comickAPI, getChapterImageUrl } from '../services/api';

const { width, height } = Dimensions.get('window');

export default function ChapterReaderScreen({ route, navigation }) {
  const { chapterHid, chapterNumber, comicTitle, groupName } = route.params;
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadChapterImages();
    
    // Set header title
    navigation.setOptions({
      title: `${comicTitle} - Ch. ${chapterNumber}`,
      headerTitleStyle: {
        fontSize: 14,
      },
    });
  }, []);

  const loadChapterImages = async () => {
    try {
      setLoading(true);
      const chapterImages = await comickAPI.getChapterImages(chapterHid);
      setImages(chapterImages || []);
    } catch (error) {
      console.error('Error loading chapter images:', error);
      Alert.alert('Error', 'Failed to load chapter images. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ff6b6b" />
        <Text style={styles.loadingText}>Loading chapter...</Text>
      </View>
    );
  }

  if (images.length === 0) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>No images found for this chapter</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar hidden />
      
      {/* Chapter Info Header */}
      <View style={styles.chapterInfo}>
        <Text style={styles.chapterTitle}>Chapter {chapterNumber}</Text>
        <Text style={styles.groupName}>{groupName}</Text>
      </View>

      {/* Images ScrollView */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        {images.map((image, index) => (
          <Image
            key={index}
            source={{ uri: getChapterImageUrl(image) }}
            style={[
              styles.chapterImage,
              {
                width: width,
                height: (image.h / image.w) * width, // Maintain aspect ratio
              }
            ]}
            contentFit="cover"
            placeholder="https://via.placeholder.com/800x1200/333/fff?text=Loading..."
            transition={200}
          />
        ))}
        
        {/* End of Chapter */}
        <View style={styles.endOfChapter}>
          <Text style={styles.endText}>End of Chapter {chapterNumber}</Text>
          <Text style={styles.endSubtext}>Scanned by {groupName}</Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
  chapterInfo: {
    backgroundColor: '#1a1a1a',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  chapterTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  groupName: {
    color: '#888',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 2,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  chapterImage: {
    backgroundColor: '#000',
    // No margin, padding, or border to ensure seamless connection
  },
  endOfChapter: {
    backgroundColor: '#1a1a1a',
    paddingVertical: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  endText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  endSubtext: {
    color: '#888',
    fontSize: 14,
  },
});
