import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
  Dimensions,
} from 'react-native';
import { Image } from 'expo-image';
import { comickAPI, getCoverImageUrl, formatStatus } from '../services/api';

const { width } = Dimensions.get('window');

export default function HomeScreen({ navigation }) {
  const [trendingComics, setTrendingComics] = useState([]);
  const [latestChapters, setLatestChapters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [trending, latest] = await Promise.all([
        comickAPI.getTrendingComics(),
        comickAPI.getLatestChapters({ limit: 15 }),
      ]);
      
      setTrendingComics(trending.rank || []);
      setLatestChapters(latest || []);
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Error', 'Failed to load data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const navigateToDetail = (comic) => {
    // For trending comics, we need to get the hid from the API response
    navigation.navigate('ManhwaDetail', {
      slug: comic.slug,
      hid: comic.hid || null, // hid might not be available in trending
      title: comic.title
    });
  };

  const navigateToDetailFromChapter = (chapter) => {
    const comic = chapter.md_comics;
    navigation.navigate('ManhwaDetail', { 
      slug: comic.slug,
      hid: comic.hid,
      title: comic.title 
    });
  };

  const renderTrendingItem = ({ item, index }) => (
    <TouchableOpacity
      style={styles.trendingItem}
      onPress={() => navigateToDetail(item)}
    >
      <View style={styles.rankBadge}>
        <Text style={styles.rankText}>#{index + 1}</Text>
      </View>
      <Image
        source={{ uri: getCoverImageUrl(item.md_covers?.[0]) }}
        style={styles.trendingImage}
        contentFit="cover"
        placeholder="https://via.placeholder.com/150x200/333/fff?text=No+Image"
      />
      <View style={styles.trendingInfo}>
        <Text style={styles.trendingTitle} numberOfLines={2}>
          {item.title}
        </Text>
        <Text style={styles.trendingChapter}>
          Ch. {item.last_chapter}
        </Text>
        <Text style={styles.trendingStatus}>
          {item.status ? formatStatus(item.status) : 'Ongoing'}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderLatestItem = ({ item }) => (
    <TouchableOpacity
      style={styles.latestItem}
      onPress={() => navigateToDetailFromChapter(item)}
    >
      <Image
        source={{ uri: getCoverImageUrl(item.md_comics?.md_covers?.[0]) }}
        style={styles.latestImage}
        contentFit="cover"
        placeholder="https://via.placeholder.com/80x100/333/fff?text=No+Image"
      />
      <View style={styles.latestInfo}>
        <Text style={styles.latestTitle} numberOfLines={2}>
          {item.md_comics?.title}
        </Text>
        <Text style={styles.latestChapter}>
          Chapter {item.chap}
        </Text>
        <Text style={styles.latestGroup}>
          {item.group_name?.[0] || 'Unknown'}
        </Text>
        <Text style={styles.latestTime}>
          {new Date(item.created_at).toLocaleDateString()}
        </Text>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Trending Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🔥 Trending Manhwa</Text>
        <FlatList
          data={trendingComics.slice(0, 10)}
          renderItem={renderTrendingItem}
          keyExtractor={(item, index) => `trending-${item.slug}-${index}`}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.trendingList}
        />
      </View>

      {/* Latest Chapters Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📚 Latest Chapters</Text>
        <FlatList
          data={latestChapters}
          renderItem={renderLatestItem}
          keyExtractor={(item, index) => `latest-${item.id}-${index}`}
          scrollEnabled={false}
          contentContainerStyle={styles.latestList}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0a0a0a',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0a0a0a',
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
  },
  section: {
    marginVertical: 10,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginHorizontal: 15,
    marginBottom: 10,
  },
  trendingList: {
    paddingHorizontal: 10,
  },
  trendingItem: {
    width: 140,
    marginHorizontal: 5,
    backgroundColor: '#1a1a1a',
    borderRadius: 8,
    overflow: 'hidden',
  },
  rankBadge: {
    position: 'absolute',
    top: 5,
    left: 5,
    backgroundColor: '#ff6b6b',
    borderRadius: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
    zIndex: 1,
  },
  rankText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  trendingImage: {
    width: '100%',
    height: 180,
  },
  trendingInfo: {
    padding: 8,
  },
  trendingTitle: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  trendingChapter: {
    color: '#ff6b6b',
    fontSize: 10,
    marginBottom: 2,
  },
  trendingStatus: {
    color: '#888',
    fontSize: 10,
  },
  latestList: {
    paddingHorizontal: 15,
  },
  latestItem: {
    flexDirection: 'row',
    backgroundColor: '#1a1a1a',
    borderRadius: 8,
    marginBottom: 10,
    overflow: 'hidden',
  },
  latestImage: {
    width: 60,
    height: 80,
  },
  latestInfo: {
    flex: 1,
    padding: 10,
    justifyContent: 'space-between',
  },
  latestTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  latestChapter: {
    color: '#ff6b6b',
    fontSize: 12,
    marginBottom: 2,
  },
  latestGroup: {
    color: '#888',
    fontSize: 10,
    marginBottom: 2,
  },
  latestTime: {
    color: '#666',
    fontSize: 10,
  },
});
