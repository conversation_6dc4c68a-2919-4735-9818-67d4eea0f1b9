import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
  Dimensions,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
// Import all necessary animation tools from Reanimated
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
  interpolateColor,
} from 'react-native-reanimated';
import { comickAPI, getCoverImageUrl } from '../services/api';

const { width, height } = Dimensions.get('window');

// --- Helper function for formatting like counts ---
const formatFollowCount = (count) => {
  if (!count) return '0';
  if (count < 1000) return count.toString();
  if (count < 1000000) return `${(count / 1000).toFixed(1).replace('.0', '')}k`;
  return `${(count / 1000000).toFixed(1).replace('.0', '')}M`;
};

// --- Deconstructed, Reusable UI Components ---

const CarouselItem = React.memo(({ item, onPress }) => (
  <TouchableOpacity activeOpacity={0.9} style={styles.carouselCard} onPress={onPress}>
    <Image
      source={{ uri: getCoverImageUrl(item.md_covers?.[0]) }}
      style={styles.carouselImage}
      placeholder="blurhash|L6A,h2t70000_2t7IVxu00Rj?bRj"
      transition={500}
    />
    <LinearGradient
      colors={['transparent', 'rgba(21, 21, 21, 0)', 'rgba(21, 21, 21, 0.9)', '#1F1D2B']}
      style={styles.carouselGradient}
    />
    <View style={styles.carouselInfo}>
      <Text style={styles.carouselCategory}>New episodes</Text>
      <Text style={styles.carouselTitle} numberOfLines={1}>{item.title}</Text>
      <View style={styles.likesContainer}>
        <Ionicons name="heart" size={16} color="#FFF" />
        <Text style={styles.likesText}>{formatFollowCount(item.user_follow_count)}</Text>
      </View>
    </View>
  </TouchableOpacity>
));



const Dot = ({ active }) => {
    const animation = useSharedValue(active ? 1 : 0);

    useEffect(() => {
        // Animate to 1 if active, or 0 if inactive, using a spring effect.
        animation.value = withSpring(active ? 1 : 0, { damping: 15 });
    }, [active]);

    // This creates the animated style object.
    const animatedStyle = useAnimatedStyle(() => {
        // Interpolate the width: 0 -> 6 (circle), 1 -> 16 (pill)
        const width = interpolate(animation.value, [0, 1], [6, 16]);
        // Interpolate the color: 0 -> grey, 1 -> white
        const backgroundColor = interpolateColor(
            animation.value,
            [0, 1],
            ['rgba(255, 255, 255, 0.4)', '#FFF']
        );
        return { width, backgroundColor };
    });

    return <Animated.View style={[styles.dot, animatedStyle]} />;
};


// FIX: The PaginationDots component is now simpler. It just maps and renders a Dot for each item.
const PaginationDots = ({ data, activeIndex }) => (
    <View style={styles.paginationContainer}>
        {data.map((_, i) => (
          <Dot key={`dot-${i}`} active={i === activeIndex} />
        ))}
    </View>
);

const SectionHeader = ({ title }) => (
    <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <TouchableOpacity>
            <Text style={styles.seeAllText}>see all</Text>
        </TouchableOpacity>
    </View>
);

const PosterItem = React.memo(({ item, onPress }) => (
    <TouchableOpacity activeOpacity={0.8} style={styles.posterCard} onPress={onPress}>
        <Image
          source={{ uri: getCoverImageUrl(item.md_covers?.[0]) }}
          style={styles.posterImage}
          placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj["
        />
        <View style={styles.posterInfo}>
          <Text style={styles.posterTitle} numberOfLines={1}>{item.title}</Text>
          <View style={styles.likesContainer}>
              <Ionicons name="heart" size={12} color="#A39DCE" />
              <Text style={styles.posterLikes}>{formatFollowCount(item.user_follow_count)}</Text>
          </View>
        </View>
    </TouchableOpacity>
));

const LoadingScreen = () => (
  <View style={[styles.container, {justifyContent: 'center', alignItems: 'center'}]}>
    <ActivityIndicator size="large" color="#FFF" />
  </View>
);

// --- Main HomeScreen ---
export default function HomeScreen({ navigation }) {
  const [featured, setFeatured] = useState([]);
  const [popular, setPopular] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);

  const carouselViewabilityConfig = { viewAreaCoveragePercentThreshold: 50 };
  const onCarouselViewableItemsChanged = useCallback(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      setActiveIndex(viewableItems[0].index || 0);
    }
  }, []);

  const loadData = async () => {
    try {
      const trendingData = await comickAPI.getTrendingComics();
      const comics = trendingData.rank || [];
      setFeatured(comics.slice(0, 3));
      setPopular(comics.slice(3, 10));
    } catch (error) {
      Alert.alert('Error', 'Failed to load data. Please try again.');
    } finally {
      setLoading(false); setRefreshing(false);
    }
  };

  useEffect(() => { loadData() }, []);
  const onRefresh = useCallback(() => { setRefreshing(true); loadData() }, []);

  const navigateToDetail = (comic) => {
    navigation.navigate('ManhwaDetail', { slug: comic.slug, hid: comic.hid, title: comic.title });
  };

  if (loading && !refreshing) {
    return <LoadingScreen />;
  }

  // --- List Data & Rendering ---
  const screenData = [
      { type: 'top_manga', title: 'Top mangas', data: popular },
      { type: 'continue_reading', title: 'Continue reading', data: popular.slice().reverse() },
  ];

  return (
    <View style={styles.container}>
      
      <FlatList
        data={screenData}
        keyExtractor={(item) => item.type}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#FFF" />}
        ListHeaderComponent={
          <>
            <View style={{height: height * 0.60}}>
                <FlatList
                  data={featured}
                  renderItem={({ item }) => <CarouselItem item={item} onPress={() => navigateToDetail(item)} />}
                  keyExtractor={(item) => `featured-${item.slug}`}
                  horizontal
                  pagingEnabled
                  showsHorizontalScrollIndicator={false}
                  onViewableItemsChanged={onCarouselViewableItemsChanged}
                  viewabilityConfig={carouselViewabilityConfig}
                />
            </View>
            <PaginationDots data={featured} activeIndex={activeIndex} />
          </>
        }
        renderItem={({item}) => (
          <View style={styles.contentSheet}>
              <SectionHeader title={item.title}/>
              <FlatList
                data={item.data}
                renderItem={({item: poster}) => <PosterItem item={poster} onPress={() => navigateToDetail(poster)}/>}
                keyExtractor={(poster) => `${item.type}-${poster.slug}`}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{paddingHorizontal: 15}}
              />
          </View>
        )}
      />
      {/* Absolute positioned Top Bar to sit on top of everything */}
      <View style={styles.topBar}>
          <Text style={styles.logo}>M</Text>
          <View style={styles.topBarIcons}>
              <TouchableOpacity style={styles.iconBg}><Ionicons name="search" size={20} color="#FFF"/></TouchableOpacity>
              <TouchableOpacity style={styles.iconBg}><Ionicons name="settings-outline" size={20} color="#FFF"/></TouchableOpacity>
          </View>
      </View>
    </View>
  );
}

// --- Stylesheet Inspired by the Image ---
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1F1D2B',
  },
  topBar: {
      position: 'absolute',
      top: 50, // In a real app, use `useSafeAreaInsets().top`
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      zIndex: 10,
  },
  logo: {
      fontSize: 32,
      fontWeight: 'bold',
      color: '#FFF',
  },
  topBarIcons: {
      flexDirection: 'row',
  },
  iconBg: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: 'rgba(50, 50, 50, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: 10,
  },
  carouselCard: {
      width: width,
      height: '100%',
      justifyContent: 'flex-end',
  },
  carouselImage: {
      ...StyleSheet.absoluteFillObject,
  },
  carouselGradient: {
      ...StyleSheet.absoluteFillObject,
  },
  carouselInfo: {
      padding: 20,
      paddingBottom: 40,
  },
  carouselCategory: {
      color: '#A39DCE',
      fontSize: 14,
      fontWeight: '600',
      marginBottom: 5,
  },
  carouselTitle: {
      color: '#FFF',
      fontSize: 28,
      fontWeight: 'bold',
      marginBottom: 8,
  },
  likesContainer: {
      flexDirection: 'row',
      alignItems: 'center',
  },
  likesText: {
      color: '#FFF',
      fontSize: 14,
      fontWeight: '600',
      marginLeft: 6,
  },
  paginationContainer: {
    flexDirection: 'row',
    alignSelf: 'center',
    alignItems: 'center', // Added for vertical alignment
    justifyContent: 'center', // Added for horizontal alignment
    height: 16, // Give the container a fixed height
    position: 'absolute',
    top: height * 0.55 - 30,
    zIndex: 5,
  },
  dot: {
    // No need for a separate dotActive style anymore
    height: 6,
    borderRadius: 3,
    marginHorizontal: 4,
  },
  contentSheet: {
    backgroundColor: '#1F1D2B',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -20,
    paddingTop: 10,
    paddingBottom: 20
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingLeft: 15,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFF',
  },
  seeAllText: {
      color: '#A39DCE',
      fontSize: 14,
      fontWeight: '600',
  },
  posterCard: {
    width: 140,
    marginRight: 15,
  },
  posterImage: {
    width: 140,
    height: 210,
    borderRadius: 16,
    backgroundColor: '#333'
  },
  posterInfo: {
      marginTop: 10,
  },
  posterTitle: {
      color: '#FFF',
      fontSize: 15,
      fontWeight: '600',
      marginBottom: 5,
  },
  posterLikes: {
      color: '#A39DCE',
      fontSize: 12,
      fontWeight: '600',
      marginLeft: 4,
  },
});