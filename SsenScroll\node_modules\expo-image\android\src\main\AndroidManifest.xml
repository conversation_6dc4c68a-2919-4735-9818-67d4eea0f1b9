<manifest xmlns:android="http://schemas.android.com/apk/res/android">
  <!-- Begin Glide configuration -->
  <!-- Internet access (https://bumptech.github.io/glide/doc/download-setup.html#internet) -->
  <uses-permission android:name="android.permission.INTERNET" />
  <!--
  Allows Glide to monitor connectivity status and restart failed requests if users go from a
  a disconnected to a connected network state.
  -->
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

  <!-- Local storage (https://bumptech.github.io/glide/doc/download-setup.html#local-storage) -->
  <uses-permission
    android:name="android.permission.READ_EXTERNAL_STORAGE"
    android:maxSdkVersion="32" />

  <!-- End Glide configuration -->
</manifest>
