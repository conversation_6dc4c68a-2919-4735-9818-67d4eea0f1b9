plugins {
  id 'com.android.library'
  id 'kotlin-kapt'
  id 'expo-module-gradle-plugin'
}

android {
  namespace "expo.modules.image"
  defaultConfig {
    versionCode 1
    versionName "2.4.0"
    consumerProguardFiles("proguard-rules.pro")

    buildConfigField("boolean", "ALLOW_GLIDE_LOGS", project.properties.get("EXPO_ALLOW_GLIDE_LOGS", "false"))
  }

  sourceSets {
    main {
      java {
        if (expoModule.safeExtGet("excludeAppGlideModule", false)) {
          exclude("**/ExpoImageAppGlideModule.kt")
        }
      }
    }
  }
}

dependencies {
  def GLIDE_VERSION = "4.16.0"

  implementation 'com.facebook.react:react-android'

  api "com.github.bumptech.glide:glide:${GLIDE_VERSION}"
  kapt "com.github.bumptech.glide:compiler:${GLIDE_VERSION}"
  api 'com.caverock:androidsvg-aar:1.4'

  implementation "com.github.penfeizhou.android.animation:glide-plugin:3.0.5"
  implementation "com.github.bumptech.glide:avif-integration:${GLIDE_VERSION}"

  api 'com.github.bumptech.glide:okhttp3-integration:4.11.0'
  api "com.squareup.okhttp3:okhttp:${expoModule.safeExtGet("okHttpVersion", '4.9.2')}"

  implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.5.1'
  implementation "jp.wasabeef:glide-transformations:4.3.0"
}
